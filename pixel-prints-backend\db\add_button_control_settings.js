const db = require('./index');

async function addButtonControlSettings() {
  try {
    // Check if settings already exist
    const checkResult = await db.query(
      'SELECT * FROM settings WHERE key = $1 OR key = $2',
      ['tshirt_button_enabled', 'mug_button_enabled']
    );

    if (checkResult.rows.length === 0) {
      // Add button control settings
      await db.query(`
        INSERT INTO settings (key, value, description)
        VALUES
          ('tshirt_button_enabled', 'true', 'Whether the Design T-Shirt button is visible on the home page'),
          ('mug_button_enabled', 'true', 'Whether the Design Mug button is visible on the home page');
      `);
      console.log('Button control settings added successfully');
    } else {
      console.log('Button control settings already exist');
    }
  } catch (error) {
    console.error('Error adding button control settings:', error);
  } finally {
    // The db module doesn't expose the pool directly, so we can't call end()
    // The connection will be closed when the process exits
    process.exit(0);
  }
}

addButtonControlSettings();
