/* User Dashboard Container */
.user-dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Arial', sans-serif;
}

/* Login Prompt */
.login-prompt {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: #666;
}

/* Dashboard Actions */
.dashboard-actions {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.back-button,
.profile-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 1rem;
  height: 36px;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.back-button {
  background-color: #f5f5f5;
  color: #333;
}

.back-button:hover {
  background-color: #e9e9e9;
}

.profile-button {
  background-color: #1976D2;
  color: white;
}

.profile-button:hover {
  background-color: #1565c0;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 2rem;
  text-align: center;
}

.dashboard-header h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.5rem;
}

.dashboard-header p {
  color: #666;
  font-size: 1.1rem;
}

/* Stats Container */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card h3 {
  color: #666;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.stat-card .stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #333;
}

/* Order Filters */
.order-filters {
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
}

.order-filters h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #333;
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-buttons button {
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  height: 36px;
  min-width: 80px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.filter-buttons button:hover {
  background-color: #e9e9e9;
}

.filter-buttons button.active {
  background-color: #1976D2;
  color: white;
  border: none;
}

/* Orders Container */
.orders-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.orders-container > div {
  cursor: pointer;
}

/* Loading, Error, and No Orders Messages */
.loading-message,
.error-message,
.no-orders-message {
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-message {
  color: #666;
}

.error-message {
  color: #d32f2f;
}

.no-orders-message {
  color: #666;
}

/* Order Details Modal */
.order-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
}

.modal-header h2 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.modal-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Common button styles */
.update-status-button,
.delete-button,
.close-button,
.cancel-button,
.confirm-delete-button,
.modal-footer .close-button {
  height: 36px;
  min-width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 500;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.update-status-button {
  background-color: #1976d2;
  color: white;
  padding: 0 12px;
  margin-right: 0.5rem;
}

.update-status-button:hover {
  background-color: #1565c0;
}

.delete-button {
  background-color: #d32f2f;
  color: white;
  padding: 0 12px;
  margin-right: 0.5rem;
}

.delete-button:hover {
  background-color: #b71c1c;
}

.close-button {
  background-color: #757575;
  color: white;
  font-size: 1.2rem;
  width: 36px;
}

.close-button:hover {
  background-color: #616161;
}

/* Close button in modal footer */
.modal-footer .close-button {
  width: auto;
  padding: 0 20px;
  font-size: 1rem;
  background-color: #616161;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

.order-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.info-group {
  margin-bottom: 1.5rem;
}

.info-group h3 {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.info-group p {
  font-size: 1.1rem;
  color: #333;
}

.info-group .price {
  font-weight: bold;
  font-size: 1.3rem;
  color: #1976D2;
}

/* Status Badge Styles */
.status-badge {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  border-radius: 0;
  font-weight: 500;
  font-size: 0.9rem;
}

.status-badge.pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.status-badge.processing {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-badge.completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-badge.cancelled {
  background-color: #ffebee;
  color: #c62828;
}

/* Delete Confirmation Modal */
.delete-confirmation-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
}

.confirmation-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-button {
  background-color: #9e9e9e;
  color: white;
  padding: 0 16px;
}

.confirm-delete-button {
  background-color: #d32f2f;
  color: white;
  padding: 0 16px;
}



.cancel-button:hover {
  background-color: #757575;
}

.confirm-delete-button:hover {
  background-color: #b71c1c;
}

.cancel-button:disabled,
.confirm-delete-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .user-dashboard-container {
    padding: 1rem;
  }

  .stats-container {
    grid-template-columns: 1fr;
  }

  .order-info {
    grid-template-columns: 1fr;
  }
}
