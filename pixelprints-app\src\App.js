


"use client"

import { useState, useEffect, useRef, useCallback, memo } from "react"
import { PDFDocument } from "pdf-lib"
import { Routes, Route, useNavigate, Navigate } from "react-router-dom"
import useDocumentTitle from "./hooks/useDocumentTitle"
import { getApiUrl } from "./utils/api"
import "./App.css"
import CustomNumberInput from "./components/CustomNumberInput"
import logger from "./utils/logger"
import Auth from "./components/Auth/Auth"
import ForgotPassword from "./components/Auth/ForgotPassword"
import ResetPassword from "./components/Auth/ResetPassword"
import UserDashboardPage from "./pages/UserDashboardPage"
import AdminDashboardPage from "./pages/AdminDashboardPage"
import AdminLoginPage from "./pages/AdminLoginPage"
import AdminAccessPage from "./pages/AdminAccessPage"
import AdminForgotPassword from "./pages/AdminForgotPassword"
import AdminResetPassword from "./pages/AdminResetPassword"
import TShirtDesignerPage from "./pages/TShirtDesignerPage"
import Logo from "./components/Logo"

function App() {
  // Set the document title to remove "React" from the address bar
  useDocumentTitle("Pixel Prints | Professional Document & Photo Printing Services")

  const navigate = useNavigate()

  // User state
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [userName, setUserName] = useState("Guest")
  const [userEmail, setUserEmail] = useState("")
  const [userRole, setUserRole] = useState("user") // 'user' or 'admin'
  const [currentView, setCurrentView] = useState("home") // 'home', 'dashboard', 'admin', or 'adminLogin'
  const [userProfile, setUserProfile] = useState(null) // Store user profile data

  // Button visibility settings
  const [buttonSettings, setButtonSettings] = useState({
    tshirt_button_enabled: true,
    mug_button_enabled: true
  })

  // Form ref for manual submission
  const orderFormRef = useRef(null)

  // Form states
  const [files, setFiles] = useState([])
  const [printType, setPrintType] = useState("black")
  const [customerName, setCustomerName] = useState("")
  const [customerEmail, setCustomerEmail] = useState("")
  const [address, setAddress] = useState("")
  // Add states for region and province selection
  const [selectedRegionGroup, setSelectedRegionGroup] = useState("")
  const [selectedRegion, setSelectedRegion] = useState("")
  const [province, setProvince] = useState("")
  const [deliveryCharge, setDeliveryCharge] = useState(0)
  const [assistedProvinces, setAssistedProvinces] = useState([])
  const [totalPages, setTotalPages] = useState(0)
  const [totalAmount, setTotalAmount] = useState(0)
  const [fileError, setFileError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [orderNumber, setOrderNumber] = useState("")
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false)
  const [serverError, setServerError] = useState("")
  const [orderId, setOrderId] = useState(null)
  const [dailyOrderLimitReached, setDailyOrderLimitReached] = useState(false)
  const [siteNotification, setSiteNotification] = useState({
    message: '',
    enabled: false
  })

  // Updated price configuration
  const prices = {
    black: 5.0,
    colored: 8.0,
  }

  // New delivery fee configuration
  const deliveryFees = {
    NCR: 80,
    Bulacan: 50,
    others: 100
  }

  // Structured list of Luzon provinces by region
  const luzonRegions = {
    "Northern Luzon (Regions I, II, CAR)": {
      "Region I (Ilocos Region)": ["Ilocos Norte", "Ilocos Sur", "La Union", "Pangasinan"],
      "Region II (Cagayan Valley)": ["Batanes", "Cagayan", "Isabela", "Nueva Vizcaya", "Quirino"],
      "CAR (Cordillera Administrative Region)": ["Abra", "Apayao", "Benguet", "Ifugao", "Kalinga", "Mountain Province"],
    },
    "Central Luzon (Region III)": {
      "Region III": ["Aurora", "Bataan", "Bulacan", "Nueva Ecija", "Pampanga", "Tarlac", "Zambales"],
    },
    "Southern Tagalog (Regions IV-A, IV-B, NCR)": {
      "Region IV-A (CALABARZON)": ["Batangas", "Cavite", "Laguna", "Quezon", "Rizal"],
      "Region IV-B (MIMAROPA)": ["Marinduque", "Occidental Mindoro", "Oriental Mindoro", "Palawan", "Romblon"],
      "NCR (National Capital Region)": [
        "Manila",
        "Caloocan",
        "Las Piñas",
        "Makati",
        "Malabon",
        "Mandaluyong",
        "Marikina",
        "Muntinlupa",
        "Navotas",
        "Parañaque",
        "Pasay",
        "Pasig",
        "Pateros",
        "Quezon City",
        "San Juan",
        "Taguig",
        "Valenzuela",
      ],
    },
    "Bicol Region (Region V)": {
      "Region V": ["Albay", "Camarines Norte", "Camarines Sur", "Catanduanes", "Masbate", "Sorsogon"],
    },
  }

  // Flat list of all provinces for easy lookup
  const allProvinces = Object.values(luzonRegions)
    .flatMap((regionGroup) => Object.values(regionGroup))
    .flat()

  // Reset dependent dropdowns when parent changes
  useEffect(() => {
    setSelectedRegion("")
    setProvince("")
  }, [selectedRegionGroup])

  useEffect(() => {
    setProvince("")
  }, [selectedRegion])

  // Create a province lookup map for O(1) access time
  const [provinceLookupMap] = useState(() => {
    const lookupMap = new Map();

    // Build the lookup map once
    for (const regionGroup in luzonRegions) {
      for (const region in luzonRegions[regionGroup]) {
        if (Array.isArray(luzonRegions[regionGroup][region])) {
          luzonRegions[regionGroup][region].forEach(province => {
            lookupMap.set(province, { regionGroup, region, province });
          });
        }
      }
    }

    return lookupMap;
  });

  // Function to find region group and region for a given province - O(1) lookup time
  const findProvinceLocation = (targetProvince) => {
    try {
      if (!targetProvince) {
        console.error('No target province provided');
        return null;
      }

      // Direct lookup instead of nested loops
      const location = provinceLookupMap.get(targetProvince);

      if (!location) {
        console.warn('Province not found in any region:', targetProvince);
        return null;
      }

      return location;
    } catch (error) {
      console.error('Error in findProvinceLocation:', error);
      return null;
    }
  };

  // Handle province tag click
  const handleProvinceTagClick = (province) => {
    try {
      const location = findProvinceLocation(province);
      if (location && location.regionGroup && location.region && location.province) {
        console.log('Found location:', location);

        // First scroll to the location selection section
        const locationSection = document.querySelector('.location-selection');
        if (locationSection) {
          locationSection.scrollIntoView({ behavior: 'smooth' });
        }

        // Add a slight delay to make the selection more visible
        setTimeout(() => {
          try {
            // Set the values in order to trigger the cascading dropdowns
            setSelectedRegionGroup(location.regionGroup);

            // Add a slight delay for the region dropdown to update
            setTimeout(() => {
              try {
                // Verify that the region exists in the selected region group
                if (luzonRegions[location.regionGroup] &&
                    Object.keys(luzonRegions[location.regionGroup]).includes(location.region)) {
                  setSelectedRegion(location.region);

                  // Add a slight delay for the province dropdown to update
                  setTimeout(() => {
                    try {
                      // Verify that the province exists in the selected region
                      if (luzonRegions[location.regionGroup][location.region] &&
                          luzonRegions[location.regionGroup][location.region].includes(location.province)) {
                        setProvince(location.province);

                        // Calculate delivery charge for the selected province
                        const charge = calculateDeliveryCharge(location.province);
                        setDeliveryCharge(charge);

                        // Highlight the dropdowns
                        const dropdowns = document.querySelectorAll('.location-selection select');
                        dropdowns.forEach(dropdown => {
                          dropdown.classList.add('highlight-selection');
                          setTimeout(() => {
                            dropdown.classList.remove('highlight-selection');
                          }, 1500);
                        });
                      } else {
                        console.error('Province not found in selected region:', location.province);
                      }
                    } catch (error) {
                      console.error('Error setting province:', error);
                    }
                  }, 100);
                } else {
                  console.error('Region not found in selected region group:', location.region);
                }
              } catch (error) {
                console.error('Error setting region:', error);
              }
            }, 100);
          } catch (error) {
            console.error('Error setting region group:', error);
          }
        }, 500);
      } else {
        console.error('Invalid location data for province:', province);
      }
    } catch (error) {
      console.error('Error in handleProvinceTagClick:', error);
    }
  };

  // Calculate totals when files or print type changes
  useEffect(() => {
    calculateTotalPages()
    calculateTotalAmount()
  }, [files, printType, deliveryCharge])

  // Add a function to calculate delivery charge after the calculateTotalPages function
  const calculateDeliveryCharge = (selectedProvince) => {
    if (!selectedProvince) return 0

    if (selectedProvince === "Bulacan") {
      return deliveryFees.Bulacan
    } else if (selectedProvince === "NCR" || selectedProvince.includes("Metro Manila")) {
      return deliveryFees.NCR
    } else if (allProvinces.includes(selectedProvince)) {
      return deliveryFees.others
    }

    return 0
  }

  // Update the calculateTotalAmount function to include delivery charge
  const calculateTotalAmount = () => {
    const pricePerPage = prices[printType]
    const total = files.reduce((sum, file) => sum + file.pages * file.copies * pricePerPage, 0)
    setTotalAmount(total + deliveryCharge)
  }

  // Add a useEffect to update delivery charge when province changes
  useEffect(() => {
    const charge = calculateDeliveryCharge(province)
    setDeliveryCharge(charge)
  }, [province])

  // Load assisted provinces from localStorage
  useEffect(() => {
    const loadAssistedProvinces = () => {
      try {
        // Add default provinces if none are set
        const defaultProvinces = [
          "Manila", "Caloocan", "Las Piñas", "Makati", "Malabon",
          "Mandaluyong", "Marikina", "Muntinlupa", "Navotas",
          "Parañaque", "Pasay", "Pasig", "Pateros", "Quezon City",
          "San Juan", "Taguig", "Valenzuela", "Bulacan"
        ];

        const savedLocations = localStorage.getItem('assistedLocations');
        console.log('Saved locations from localStorage:', savedLocations ? 'Found' : 'Not found');

        if (savedLocations) {
          try {
            const locationsData = JSON.parse(savedLocations);
            const provinces = [];

            // Extract all assisted provinces from the nested structure
            Object.keys(locationsData).forEach(regionGroup => {
              Object.keys(locationsData[regionGroup] || {}).forEach(region => {
                Object.entries(locationsData[regionGroup][region] || {}).forEach(([province, isAssisted]) => {
                  if (isAssisted) {
                    provinces.push(province);
                  }
                });
              });
            });

            console.log('Parsed provinces from localStorage:', provinces.length);

            if (provinces.length > 0) {
              setAssistedProvinces(provinces);
            } else {
              console.log('No provinces found in localStorage, using defaults');
              setAssistedProvinces(defaultProvinces);
            }
          } catch (error) {
            console.error('Error parsing assisted locations:', error);
            console.log('Using default provinces due to parsing error');
            setAssistedProvinces(defaultProvinces);
          }
        } else {
          console.log('No saved locations found, using default provinces');
          setAssistedProvinces(defaultProvinces);
        }
      } catch (error) {
        console.error('Unexpected error in loadAssistedProvinces:', error);
      }
    };

    loadAssistedProvinces();

    // Add event listener to reload when localStorage changes
    window.addEventListener('storage', loadAssistedProvinces);

    return () => {
      window.removeEventListener('storage', loadAssistedProvinces);
    };
  }, [])

  const calculateTotalPages = () => {
    const total = files.reduce((sum, file) => sum + file.pages * file.copies, 0)
    setTotalPages(total)
  }

  const countPDFPages = async (file) => {
    try {
      const arrayBuffer = await file.arrayBuffer()
      const pdfDoc = await PDFDocument.load(arrayBuffer)
      return pdfDoc.getPages().length
    } catch (error) {
      logger.error("Error counting PDF pages:", error)
      return 0
    }
  }

  // Memoize allowed file types for better performance
  const allowedTypes = ["application/pdf", "image/jpeg", "image/jpg"];
  const maxSize = 10 * 1024 * 1024; // 10MB

  // Generate a more efficient unique ID
  const generateUniqueId = (() => {
    let counter = 0;
    const prefix = Date.now().toString(36);
    return () => `${prefix}-${(counter++).toString(36)}`;
  })();

  const handleFileUpload = async (event) => {
    const selectedFiles = Array.from(event.target.files);
    setFileError("");
    setUploadProgress(0);

    // Early validation to avoid unnecessary processing
    if (files.length + selectedFiles.length > 10) {
      setFileError("Maximum 10 files allowed");
      return;
    }

    // Validate all files in a single pass
    let hasOversizedFiles = false;
    let hasInvalidTypes = false;

    for (const file of selectedFiles) {
      if (file.size > maxSize) {
        hasOversizedFiles = true;
        break;
      }

      if (!allowedTypes.includes(file.type)) {
        hasInvalidTypes = true;
        break;
      }
    }

    if (hasOversizedFiles) {
      setFileError("Some files exceed the 10MB size limit");
      return;
    }

    if (hasInvalidTypes) {
      setFileError("Only PDF and JPEG files are allowed");
      return;
    }

    // Process files in batches to avoid blocking the UI
    const batchSize = 3;
    const processedFiles = [];

    for (let i = 0; i < selectedFiles.length; i += batchSize) {
      const batch = selectedFiles.slice(i, i + batchSize);

      // Process batch
      const batchResults = await Promise.all(
        batch.map(async (file) => {
          let pageCount = 1;
          if (file.type === "application/pdf") {
            pageCount = await countPDFPages(file);
          }
          return {
            file,
            name: file.name,
            type: file.type,
            pages: pageCount,
            size: file.size,
            id: generateUniqueId(),
            copies: 1,
          };
        })
      );

      processedFiles.push(...batchResults);

      // Update progress after each batch
      const progress = Math.min(100, Math.round(((i + batch.length) / selectedFiles.length) * 100));
      setUploadProgress(progress);

      // Allow UI to update between batches
      if (i + batchSize < selectedFiles.length) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }

    setFiles((prevFiles) => [...prevFiles, ...processedFiles]);
    setUploadProgress(100);
  }

  const removeFile = (fileId) => {
    setFiles((prevFiles) => prevFiles.filter((file) => file.id !== fileId))
  }

  const updateFileCopies = (fileId, copies) => {
    setFiles((prevFiles) => {
      return prevFiles.map((file) => {
        if (file.id === fileId) {
          return { ...file, copies: Math.max(1, Number.parseInt(copies) || 1) }
        }
        return file
      })
    })
  }

  const handleSubmit = async (event) => {
    // Prevent default if event is provided
    if (event) {
      event.preventDefault()
    }

    // Clear any previous errors
    setFileError("")
    setServerError("")

    if (!isAuthenticated) {
      setShowAuthModal(true)
      return
    }

    // Validate form
    if (!customerName || !customerEmail || !address || !province) {
      setFileError("Please fill in all required fields")
      return
    }

    // Validate files
    if (files.length === 0) {
      setFileError("Please upload at least one file")
      return
    }

    // Check daily order limit before submitting
    const limitCheck = await checkDailyOrderLimit();
    if (limitCheck.limitReached && limitCheck.enabled) {
      setServerError(`Daily order limit of ${limitCheck.limit} orders has been reached. Please try again tomorrow.`);
      return;
    }

    setIsLoading(true)
    setServerError("")
    setUploadProgress(10) // Start progress

    try {
      // Create form data for file upload
      const formData = new FormData()

      // Generate order number
      const generatedOrderNumber = "ORD-" + Date.now().toString().slice(-6)

      // Add customer information
      formData.append("orderNumber", generatedOrderNumber)
      formData.append("customerName", customerName)
      formData.append("customerEmail", customerEmail)
      formData.append("address", address)
      formData.append("province", province)
      formData.append("deliveryCharge", deliveryCharge)
      formData.append("printType", printType)
      formData.append("totalPages", totalPages)
      formData.append("price", totalAmount.toFixed(2))

      setUploadProgress(20)

      // If there are files, add them to the form data
      if (files.length > 0) {
        files.forEach((file, index) => {
          formData.append("files", file.file)
          formData.append(
            `fileInfo${index}`,
            JSON.stringify({
              name: file.name,
              pages: file.pages,
              copies: file.copies,
            })
          )
        })
        setUploadProgress(50)
      } else {
        // If no files, add a placeholder file name
        formData.append("fileName", "No file uploaded")
        formData.append("quantity", "1")
      }

      // Send order to server
      const response = await fetch(getApiUrl('/send-order'), {
        method: 'POST',
        body: formData,
      })

      setUploadProgress(90)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to place order")
      }

      const data = await response.json()
      setOrderNumber(data.orderNumber)
      setOrderId(data.orderId) // Add this state if you want to track the database ID
      setIsConfirmationDialogOpen(true)

      // Reset form
      resetForm()

      setUploadProgress(100)
    } catch (error) {
      logger.error('Error placing order:', error)
      setServerError(error.message || "Failed to place order. Please try again.")
    } finally {
      setIsLoading(false)
      setTimeout(() => setUploadProgress(0), 1000)
    }
  }

  const resetForm = () => {
    setFiles([])
    setPrintType("black")
    setAddress("")
    setCustomerName("")
    setCustomerEmail("")
    setTotalPages(0)
    setTotalAmount(0)
    setOrderId(null)
    setSelectedRegionGroup("")
    setSelectedRegion("")
    setProvince("")
    setDeliveryCharge(0)
  }

  useEffect(() => {
    // Check for existing authentication
    const storedAuth = localStorage.getItem("isAuthenticated")
    const storedName = localStorage.getItem("userName")
    const storedEmail = localStorage.getItem("userEmail")

    if (storedAuth === "true" && storedName) {
      setIsAuthenticated(true)
      setUserName(storedName)
      if (storedEmail) {
        setUserEmail(storedEmail)
      }
    }
  }, [])

  // Simple auth modal component
  // Check daily order limit
  const checkDailyOrderLimit = async () => {
    try {
      const response = await fetch(getApiUrl('/api/settings/daily-order-limit/check'));
      if (!response.ok) {
        throw new Error('Failed to check daily order limit');
      }

      const data = await response.json();
      setDailyOrderLimitReached(data.limitReached && data.enabled);

      return data;
    } catch (error) {
      logger.error('Error checking daily order limit:', error);
      return { limitReached: false, enabled: false };
    }
  };

  // Check for site notifications
  const checkSiteNotification = async () => {
    // Don't check notifications when auth modal is open to prevent flickering
    if (showAuthModal) {
      return;
    }

    // In development mode, use mock data
    const isDevelopment = process.env.NODE_ENV === 'development';

    try {
      let data;

      if (isDevelopment) {
        // Use mock data in development mode
        data = [
          { key: 'site-notification-message', value: 'Welcome to PixelPrints Development Environment' },
          { key: 'site-notification-enabled', value: 'true' }
        ];
      } else {
        // In production, fetch from API
        const response = await fetch(getApiUrl('/api/settings'));
        if (!response.ok) {
          throw new Error('Failed to fetch settings');
        }
        data = await response.json();
      }

      // Find site notification settings
      const messageObj = data.find(setting => setting.key === 'site-notification-message');
      const enabledObj = data.find(setting => setting.key === 'site-notification-enabled');

      if (messageObj && enabledObj) {
        const newMessage = messageObj.value;
        const newEnabled = enabledObj.value === 'true';

        // Only update state if the values have changed
        setSiteNotification(prevState => {
          if (prevState.message === newMessage && prevState.enabled === newEnabled) {
            return prevState; // No change needed
          }

          // Log only when there's an actual change
          logger.info('Site notification updated:', {
            message: newMessage,
            enabled: newEnabled
          });

          return {
            message: newMessage,
            enabled: newEnabled
          };
        });
      } else {
        // If settings don't exist, make sure notification is disabled
        setSiteNotification(prevState => {
          if (!prevState.enabled && prevState.message === '') {
            return prevState; // Already disabled, no change needed
          }

          logger.info('Site notification disabled (settings not found)');
          return {
            message: '',
            enabled: false
          };
        });
      }
    } catch (error) {
      logger.error('Error checking site notification:', error);
      // In case of error, make sure notification is disabled
      setSiteNotification(prevState => {
        if (!prevState.enabled && prevState.message === '') {
          return prevState; // Already disabled, no change needed
        }

        return {
          message: '',
          enabled: false
        };
      });
    }
  };

  // Function to fetch button settings
  const fetchButtonSettings = async () => {
    try {
      const response = await fetch(getApiUrl('/api/settings'));
      if (response.ok) {
        const settings = await response.json();

        // Convert settings array to object for easier access
        const settingsObj = {};
        settings.forEach(setting => {
          settingsObj[setting.key] = setting.value;
        });

        // Update button settings state
        setButtonSettings({
          tshirt_button_enabled: settingsObj.tshirt_button_enabled === 'true',
          mug_button_enabled: settingsObj.mug_button_enabled === 'true'
        });
      }
    } catch (error) {
      console.error('Error fetching button settings:', error);
      // Keep default values if fetch fails
    }
  };

  // Check for authentication and daily order limit on component mount
  useEffect(() => {
    // Check for existing authentication
    const storedAuth = localStorage.getItem("isAuthenticated")
    const storedName = localStorage.getItem("userName")
    const storedEmail = localStorage.getItem("userEmail")
    const storedRole = localStorage.getItem("userRole")

    if (storedAuth === "true" && storedName) {
      setIsAuthenticated(true)
      setUserName(storedName)
      if (storedEmail) {
        setUserEmail(storedEmail)
      }
      if (storedRole) {
        setUserRole(storedRole)
      }
    }

    // Sync currentView state with URL
    const path = window.location.pathname;
    if (path === '/') {
      setCurrentView('home');
    } else if (path === '/dashboard') {
      setCurrentView('dashboard');
    } else if (path === '/admin') {
      setCurrentView('admin');
    } else if (path === '/admin-access') {
      setCurrentView('adminAccess');
    } else if (path === '/tshirt-designer') {
      setCurrentView('tshirtDesigner');
    }

    // Check daily order limit, site notifications, and button settings
    checkDailyOrderLimit();
    checkSiteNotification();
    fetchButtonSettings();

    // Set up interval to check daily order limit every 5 minutes, site notifications every 2 minutes, and button settings every 10 minutes
    const dailyLimitIntervalId = setInterval(checkDailyOrderLimit, 5 * 60 * 1000);
    const notificationIntervalId = setInterval(checkSiteNotification, 2 * 60 * 1000);
    const buttonSettingsIntervalId = setInterval(fetchButtonSettings, 10 * 60 * 1000);

    return () => {
      clearInterval(dailyLimitIntervalId);
      clearInterval(notificationIntervalId);
      clearInterval(buttonSettingsIntervalId);
    };
  }, []);

  // Memoize the auth success callback to prevent unnecessary re-renders
  const handleAuthSuccess = useCallback((user) => {
    setIsAuthenticated(true)
    setUserName(user.name)
    setUserEmail(user.email)
    // Check if this is the admin email
    const isAdmin = user.email === "<EMAIL>";
    setUserRole(isAdmin ? "admin" : "user")
    setShowAuthModal(false)
    // Store user data
    localStorage.setItem("isAuthenticated", "true")
    localStorage.setItem("userName", user.name)
    localStorage.setItem("userEmail", user.email)
    localStorage.setItem("userRole", isAdmin ? "admin" : "user")
  }, []);

  // Memoize the close modal function
  const handleCloseModal = useCallback(() => {
    setShowAuthModal(false)
  }, []);

  // Memoize the AuthModal component to prevent re-renders
  const AuthModal = memo(() => (
    <div className="modal-overlay">
      <div className="modal-content auth-modal">
        <Auth onAuthSuccess={handleAuthSuccess} />
        <button onClick={handleCloseModal} className="modal-close-button">
          ×
        </button>
      </div>
    </div>
  ))

  // Determine what content to show based on the current route
  const renderMainContent = () => {
    const path = window.location.pathname;

    // Set specific page titles based on the current route
    if (path === '/dashboard') {
      document.title = "My Dashboard | Pixel Prints";
    } else if (path === '/admin') {
      document.title = "Admin Dashboard | Pixel Prints";
    } else if (path === '/admin-login') {
      document.title = "Admin Login | Pixel Prints";
    } else if (path === '/forgot-password') {
      document.title = "Reset Password | Pixel Prints";
    } else if (path.startsWith('/reset-password/')) {
      document.title = "Set New Password | Pixel Prints";
    } else if (path === '/tshirt-designer') {
      document.title = "T-Shirt Designer | Pixel Prints";
    } else if (path === '/') {
      document.title = "Pixel Prints | Professional Document & Photo Printing Services";
    }

    // Authentication routes
    if (path === '/forgot-password') {
      return <ForgotPassword />;
    } else if (path.startsWith('/reset-password/')) {
      return <ResetPassword />;
    } else if (path === '/admin-forgot-password') {
      return <AdminForgotPassword onBackToMain={() => { setCurrentView('home'); navigate('/'); }} />;
    } else if (path.startsWith('/admin-reset-password/')) {
      return <AdminResetPassword onBackToMain={() => { setCurrentView('home'); navigate('/'); }} />;
    }

    // Main application routes
    else if (path === '/dashboard') {
      return isAuthenticated ?
        <UserDashboardPage onBackToMain={() => { setCurrentView('home'); navigate('/'); }} /> :
        <Navigate to="/" replace />;
    } else if (path === '/admin') {
      return isAuthenticated && userRole === 'admin' ?
        <AdminDashboardPage onBackToMain={() => { setCurrentView('home'); navigate('/'); }} /> :
        <Navigate to="/" replace />;
    } else if (path === '/admin-login') {
      return <AdminLoginPage
        onLoginSuccess={(user) => {
          setIsAuthenticated(true);
          setUserName(user.name);
          setUserEmail(user.email);
          setUserRole('admin');
          setCurrentView('admin');
          navigate('/admin');
        }}
        onBackToMain={() => { setCurrentView('home'); navigate('/'); }}
      />;
    } else if (path === '/admin-access') {
      return <AdminAccessPage setCurrentView={(view) => {
        setCurrentView(view);
        if (view === 'adminLogin') navigate('/admin-login');
        else if (view === 'home') navigate('/');
      }} />;
    } else if (path === '/tshirt-designer') {
      return <TShirtDesignerPage />;
    }

    // Home route (default)
    return (
      <>
        <header className="App-header">
          <div className="header-content">
            <Logo />
            <h1 className="slogan">Professional Document & Photo Printing Services</h1>
            <p className="tagline">Knock, knock! Who's there? Your Prints - Fast delivery across Luzon</p>

          {currentView === 'home' && (
            <div className="file-upload-container">
              <h2>Upload Your Files for High-Quality Printing</h2>
              <p>Upload your PDF or JPEG files for professional document and photo printing. We offer fast, reliable printing services with delivery across Luzon.</p>
              <div className="file-input-wrapper">
                <button className="file-input-button" onClick={() => document.getElementById('fileUpload').click()}>CHOOSE FILES</button>
                <input id="fileUpload" type="file" accept=".pdf,.jpg,.jpeg" multiple onChange={handleFileUpload} />
              </div>
              <div className="file-types">Accepted file types: PDF, JPEG</div>
              {fileError && <p className="error-message">{fileError}</p>}
              {serverError && <p className="error-message">{serverError}</p>}

              {files.length > 0 && (
                <div className="file-list">
                  <h3>Uploaded Files</h3>
                  {files.map((file) => (
                    <div key={file.id} className="file-item">
                      <span>
                        {file.name} ({file.pages} pages)
                      </span>
                      <CustomNumberInput
                        value={file.copies}
                        onChange={(value) => updateFileCopies(file.id, value)}
                        min={1}
                        max={100}
                        label="Copies"
                      />
                      <button type="button" onClick={() => removeFile(file.id)}>
                        REMOVE
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
          <div className="user-info">
            {isAuthenticated ? (
              <>
                <div className="auth-buttons">
                  <span>Welcome, {userName}</span>
                  <button
                    onClick={() => {
                      // Show the dashboard
                      setCurrentView('dashboard')
                      navigate('/dashboard')
                    }}
                    className="dashboard-button"
                  >
                    My Dashboard
                  </button>
                  {userRole === 'admin' && (
                    <button
                      onClick={() => {
                        // Show the admin dashboard
                        setCurrentView('admin')
                        navigate('/admin')
                      }}
                      className="admin-button"
                    >
                      Admin Dashboard
                    </button>
                  )}
                  <button
                    onClick={() => {
                      setIsAuthenticated(false)
                      setUserName("Guest")
                      setUserRole("user")
                      localStorage.removeItem("isAuthenticated")
                      localStorage.removeItem("userName")
                      localStorage.removeItem("userEmail")
                      localStorage.removeItem("userRole")
                      setCurrentView('home')
                      navigate('/')
                    }}
                    className="logout-button"
                  >
                    Logout
                  </button>
                </div>
                <div className="design-buttons">
                  {buttonSettings.tshirt_button_enabled && (
                    <button
                      onClick={() => {
                        setCurrentView('tshirtDesigner')
                        navigate('/tshirt-designer')
                      }}
                      className="tshirt-designer-button"
                    >
                      Design a T-Shirt
                    </button>
                  )}
                  {buttonSettings.mug_button_enabled && (
                    <button
                      onClick={() => {
                        alert('Mug designer coming soon!')
                      }}
                      className="mug-designer-button"
                    >
                      Design a Mug
                    </button>
                  )}
                </div>
              </>
            ) : (
              <>
                <div className="auth-buttons">
                  <button onClick={() => setShowAuthModal(true)} className="login-button">
                    Sign Up / Login
                  </button>
                </div>
                <div className="design-buttons">
                  {buttonSettings.tshirt_button_enabled && (
                    <button
                      onClick={() => {
                        setCurrentView('tshirtDesigner')
                        navigate('/tshirt-designer')
                      }}
                      className="tshirt-designer-button"
                    >
                      Design a T-Shirt
                    </button>
                  )}
                  {buttonSettings.mug_button_enabled && (
                    <button
                      onClick={() => {
                        alert('Mug designer coming soon!')
                      }}
                      className="mug-designer-button"
                    >
                      Design a Mug
                    </button>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Main content is now handled by Routes */}
      {window.location.pathname === '/' && (
        <main className="main-content">
          {dailyOrderLimitReached && (
            <div className="order-limit-notification">
              <p>⚠️ Daily order limit has been reached. New orders will be accepted tomorrow.</p>
            </div>
          )}
          {siteNotification.enabled && (
            <div className="site-notification">
              <p>⚠️ {siteNotification.message || 'Site notification'}</p>
            </div>
          )}
          <form className="order-form" ref={orderFormRef}>
          <div className="form-group">
            <label htmlFor="customerName">Name and Contact Number</label>
            <input
              id="customerName"
              type="text"
              placeholder="name, contact number"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="customerEmail">Email</label>
            <input
              id="customerEmail"
              type="email"
              placeholder="<EMAIL>"
              value={customerEmail}
              onChange={(e) => setCustomerEmail(e.target.value)}
              required
            />
            <small className="form-hint">Order confirmation will be sent to this email. Please check your spam folder if you don't see it.</small>
          </div>

          <div className="form-group">
            <label htmlFor="address">Delivery Address</label>
            <textarea id="address" value={address} onChange={(e) => setAddress(e.target.value)} required />
          </div>

          {/* Replace the single province dropdown with a cascading dropdown system */}
          <div className="form-group location-selection">
            <label>Delivered to:</label>

            {/* Display assisted provinces information - always show this section */}
            <div className="assisted-provinces-info">
              <p>Currently assisted locations: <span className="helper-text">(click to select)</span></p>
              <div className="assisted-provinces-list">
                {assistedProvinces.sort().map(provinceItem => (
                  <button
                    key={provinceItem}
                    className={`assisted-province-tag ${provinceItem === province ? 'selected' : ''}`}
                    onClick={() => handleProvinceTagClick(provinceItem)}
                    title="Click to select this province"
                  >
                    {provinceItem}
                  </button>
                ))}
              </div>
            </div>

            {/* Step 1: Select Region Group */}
            <div className="select-container">
              <label htmlFor="regionGroup" className="sub-label">Region Group</label>
              <select
                id="regionGroup"
                value={selectedRegionGroup}
                onChange={(e) => setSelectedRegionGroup(e.target.value)}
                required
                className="region-select"
              >
                <option value="">Select Region Group</option>
                {Object.keys(luzonRegions).map((regionGroup) => (
                  <option key={regionGroup} value={regionGroup}>
                    {regionGroup}
                  </option>
                ))}
              </select>
            </div>

            {/* Step 2: Select Region (only shown if region group is selected) */}
            {selectedRegionGroup && (
              <div className="select-container">
                <label htmlFor="region" className="sub-label">Region</label>
                <select
                  id="region"
                  value={selectedRegion}
                  onChange={(e) => setSelectedRegion(e.target.value)}
                  required
                  className="region-select"
                >
                  <option value="">Select Region</option>
                  {(luzonRegions[selectedRegionGroup] ? Object.keys(luzonRegions[selectedRegionGroup]) : []).map((region) => (
                    <option key={region} value={region}>
                      {region}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Step 3: Select Province (only shown if region is selected) */}
            {selectedRegion && (
              <div className="select-container">
                <label htmlFor="province" className="sub-label">Province/City</label>
                <select
                  id="province"
                  value={province}
                  onChange={(e) => setProvince(e.target.value)}
                  required
                  className="province-select"
                >
                  <option value="">Select Province/City</option>
                  {(luzonRegions[selectedRegionGroup] && luzonRegions[selectedRegionGroup][selectedRegion] ? luzonRegions[selectedRegionGroup][selectedRegion] : []).map((prov) => {
                    const isAssisted = assistedProvinces.includes(prov);
                    return (
                      <option
                        key={prov}
                        value={prov}
                        className={isAssisted ? 'assisted-province-option' : ''}
                      >
                        {prov}{isAssisted ? ' ✓' : ''}
                      </option>
                    );
                  })}
                </select>
              </div>
            )}

            {deliveryCharge > 0 && (
              <p className="delivery-info">
                Delivery Charge: ₱{deliveryCharge.toFixed(2)}
              </p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="printType">Print Type (short)</label>
            <select id="printType" value={printType} onChange={(e) => setPrintType(e.target.value)}>
              <option value="black">Black & White (₱5.00/page)</option>
              <option value="colored">Colored (₱8.00/page)</option>
            </select>
          </div>

          {/* File upload section moved to the top */}

          {/* Update the order summary to include delivery charge */}
          <div className="order-summary">
            <div className="summary-item">
              <span>Total Pages:</span>
              <span>{totalPages}</span>
            </div>
            {deliveryCharge > 0 && (
              <div className="summary-item">
                <span>Delivery Charge:</span>
                <span>₱{deliveryCharge.toFixed(2)}</span>
              </div>
            )}
            <div className="summary-item total">
              <span>Total Amount:</span>
              <span>₱{totalAmount.toFixed(2)}</span>
            </div>
          </div>

          <button
            type="button"
            className="submit-button"
            disabled={isLoading || !customerName || !customerEmail || !address || !province || files.length === 0}
            onClick={(e) => {
              e.preventDefault();
              if (customerName && customerEmail && address && province && files.length > 0) {
                handleSubmit(e);
              } else {
                setFileError("Please fill in all required fields and upload at least one file");
              }
            }}
          >
            {isLoading ? "Processing..." : "Place Order"}
          </button>

          {uploadProgress > 0 && (
            <div className="progress-bar">
              <div className="progress-bar-fill" style={{ width: `${uploadProgress}%` }} />
            </div>
          )}
          </form>
        </main>
      )}

      {showAuthModal && <AuthModal />}

      {isConfirmationDialogOpen && (
        <div className="modal-overlay" style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'rgba(0, 0, 0, 0.5)', display: 'flex', justifyContent: 'center', alignItems: 'center', zIndex: 1000 }}>
          <div style={{ backgroundColor: 'white', padding: '30px', borderRadius: '4px', boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)', maxWidth: '500px', width: '90%', animation: 'fadeIn 0.3s ease-out' }}>
            <div style={{ textAlign: 'center' }}>
              <h2 style={{ fontSize: '2.2rem', color: '#2E7D32', marginBottom: '1.5rem', fontWeight: 600 }}>
                <span style={{ color: '#2E7D32', marginRight: '10px' }}>✓</span> Order Confirmed!
              </h2>
              <div style={{ backgroundColor: '#e3f2fd', padding: '12px', borderRadius: '4px', display: 'inline-block', margin: '1rem 0', fontSize: '1.5rem' }}>
                Order Number: <strong style={{ color: '#1976D2' }}>{orderNumber}</strong>
              </div>
              <div style={{ backgroundColor: '#f5f5f5', padding: '20px', borderRadius: '4px', margin: '1.5rem 0', fontSize: '1.2rem', textAlign: 'center' }}>
                <p style={{ marginBottom: '15px', fontWeight: 500 }}>Order confirmation has been sent to:</p>
                <p style={{ color: '#1976D2', fontWeight: 'bold', margin: '0.5rem 0' }}>
                  {customerEmail}
                </p>
                <p style={{ color: '#1976D2', fontWeight: 'bold', margin: '0.5rem 0' }}>
                  <EMAIL>
                </p>
                <p style={{ color: '#e65100', fontSize: '0.9rem', margin: '1rem 0 0 0', backgroundColor: '#fff3e0', padding: '8px', borderRadius: '4px', textAlign: 'center' }}>
                  <strong>Note:</strong> If you don't see the email in your inbox, please check your spam/junk folder.
                </p>
              </div>
              <p style={{ fontSize: '1.4rem', color: '#2E7D32', fontWeight: 500, margin: '1.5rem 0' }}>
                Thank you for choosing Pixel Prints!
              </p>
              <button
                onClick={() => {
                  setIsConfirmationDialogOpen(false);
                  // Optional: Reset form after closing
                  resetForm();
                }}
                style={{
                  padding: '12px 30px',
                  fontSize: '1.2rem',
                  backgroundColor: '#757575',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0',
                  cursor: 'pointer',
                  transition: 'background-color 0.3s ease',
                  width: '150px',
                  margin: '10px auto',
                  display: 'block',
                  fontWeight: '500',
                  textTransform: 'uppercase',
                  letterSpacing: '1px'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#616161'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#757575'}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Footer with SEO content and hidden admin access link */}
      {currentView === 'home' && window.location.pathname === '/' && (
        <footer className="app-footer">
          <div className="footer-content">
            <div className="footer-sections">
              <div className="footer-section">
                <h3>About Pixel Prints</h3>
                <p>Professional document and photo printing services with fast delivery across Luzon. We offer high-quality printing for all your needs.</p>
              </div>

              <div className="footer-section">
                <h3>Our Services</h3>
                <ul>
                  <li>Document Printing</li>
                  <li>Photo Printing</li>
                  <li>Black & White Printing</li>
                  <li>Color Printing</li>
                  <li>Fast Delivery</li>
                  <li><a href="/tshirt-designer" onClick={(e) => { e.preventDefault(); navigate('/tshirt-designer'); setCurrentView('tshirtDesigner'); }}>Custom T-Shirt Design</a></li>
                  <li><a href="#" onClick={(e) => { e.preventDefault(); alert('Mug designer coming soon!'); }}>Custom Mug Design</a></li>
                </ul>
              </div>

              <div className="footer-section">
                <h3>Service Areas</h3>
                <p>We deliver to all areas in Luzon including NCR, Bulacan, and other provinces.</p>
              </div>

              <div className="footer-section">
                <h3>Contact Us</h3>
                <p>Email: <EMAIL></p>
                <p>Phone: +639063065559</p>
              </div>
            </div>

            <div className="footer-bottom">
              <p>© {new Date().getFullYear()} Pixel Prints. All rights reserved<span className="admin-dot-link" onClick={() => { setCurrentView('adminAccess'); navigate('/admin-access'); }}>.</span></p>
            </div>
          </div>
        </footer>
      )}
      </>
    );
  };

  return (
    <div className="App">
      {renderMainContent()}
    </div>
  )
}

export default App





