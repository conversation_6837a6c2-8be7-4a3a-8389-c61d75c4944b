const db = require('../../db');

// Get all users
const getAllUsers = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT id, name, email, role, created_at
      FROM users
      ORDER BY created_at DESC
    `);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Server error while fetching users' });
  }
};

// Get customers from orders (for sites without user registration)
const getCustomersFromOrders = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT DISTINCT customer_name as name, customer_email as email,
             MIN(created_at) as created_at
      FROM orders
      GROUP BY customer_name, customer_email
      ORDER BY MIN(created_at) DESC
    `);

    // Format the data to match the users structure
    const customers = result.rows.map((customer, index) => ({
      id: `c-${index + 1}`,
      name: customer.name,
      email: customer.email,
      role: 'customer',
      created_at: customer.created_at
    }));

    res.json(customers);
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ message: 'Server error while fetching customers' });
  }
};

// Get combined users and customers
const getAllUsersAndCustomers = async (req, res) => {
  try {
    // Get registered users
    const usersResult = await db.query(`
      SELECT id, name, email, role, created_at
      FROM users
      ORDER BY created_at DESC
    `);

    // Get customers from orders
    const customersResult = await db.query(`
      SELECT DISTINCT customer_name as name, customer_email as email,
             MIN(created_at) as created_at
      FROM orders
      WHERE customer_email NOT IN (SELECT email FROM users)
      GROUP BY customer_name, customer_email
      ORDER BY MIN(created_at) DESC
    `);

    // Format customers to match user structure with error handling
    const customers = customersResult.rows
      .filter(customer => customer && customer.email) // Filter out invalid customers
      .map((customer, index) => ({
        id: `c-${index + 1}`,
        name: customer.name || 'Unknown',
        email: customer.email,
        role: 'customer',
        created_at: customer.created_at || new Date().toISOString()
      }));

    // Combine users and customers
    const allUsers = [...usersResult.rows, ...customers];

    // Sort by created_at with error handling
    allUsers.sort((a, b) => {
      try {
        // Handle potential invalid dates
        const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
        const dateB = b.created_at ? new Date(b.created_at) : new Date(0);

        // Check if dates are valid
        if (isNaN(dateA.getTime())) return 1;
        if (isNaN(dateB.getTime())) return -1;

        return dateB - dateA;
      } catch (error) {
        console.error('Error sorting dates:', error);
        return 0;
      }
    });

    res.json(allUsers);
  } catch (error) {
    console.error('Error fetching users and customers:', error);
    res.status(500).json({ message: 'Server error while fetching users and customers' });
  }
};

// Create a new user
const createUser = async (req, res) => {
  const { name, email, password, role } = req.body;

  if (!name || !email || !password) {
    return res.status(400).json({ message: 'Name, email, and password are required' });
  }

  try {
    // Check if user already exists
    const existingUser = await db.query('SELECT * FROM users WHERE email = $1', [email]);

    if (existingUser.rows.length > 0) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    // Hash password using bcrypt
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    const result = await db.query(
      'INSERT INTO users (name, email, password, role) VALUES ($1, $2, $3, $4) RETURNING id, name, email, role, created_at',
      [name, email, hashedPassword, role || 'user']
    );

    res.status(201).json({
      message: 'User created successfully',
      user: result.rows[0]
    });
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ message: 'Server error while creating user' });
  }
};

// Update a user
const updateUser = async (req, res) => {
  const { id } = req.params;
  const { name, email, role, password } = req.body;

  if (!id) {
    return res.status(400).json({ message: 'User ID is required' });
  }

  try {
    // Check if user exists
    const existingUser = await db.query('SELECT * FROM users WHERE id = $1', [id]);

    if (existingUser.rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Build the update query dynamically based on provided fields
    let updateQuery = 'UPDATE users SET ';
    const updateValues = [];
    const updateFields = [];
    let paramIndex = 1;

    if (name) {
      updateFields.push(`name = $${paramIndex}`);
      updateValues.push(name);
      paramIndex++;
    }

    if (email) {
      // Check if email is already taken by another user
      if (email !== existingUser.rows[0].email) {
        const emailCheck = await db.query('SELECT * FROM users WHERE email = $1 AND id != $2', [email, id]);
        if (emailCheck.rows.length > 0) {
          return res.status(400).json({ message: 'Email is already taken by another user' });
        }
      }

      updateFields.push(`email = $${paramIndex}`);
      updateValues.push(email);
      paramIndex++;
    }

    if (role) {
      updateFields.push(`role = $${paramIndex}`);
      updateValues.push(role);
      paramIndex++;
    }

    if (password) {
      // Hash the new password
      const bcrypt = require('bcryptjs');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      updateFields.push(`password = $${paramIndex}`);
      updateValues.push(hashedPassword);
      paramIndex++;
    }

    // If no fields to update
    if (updateFields.length === 0) {
      return res.status(400).json({ message: 'No fields to update' });
    }

    // Complete the query
    updateQuery += updateFields.join(', ');
    updateQuery += ` WHERE id = $${paramIndex} RETURNING id, name, email, role, created_at`;
    updateValues.push(id);

    // Execute the update
    const result = await db.query(updateQuery, updateValues);

    res.json({
      message: 'User updated successfully',
      user: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ message: 'Server error while updating user' });
  }
};

// Delete a user
const deleteUser = async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ message: 'User ID is required' });
  }

  try {
    // Check if user exists
    const existingUser = await db.query('SELECT * FROM users WHERE id = $1', [id]);

    if (existingUser.rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is an admin
    if (existingUser.rows[0].role === 'admin') {
      // Count admins
      const adminCount = await db.query('SELECT COUNT(*) FROM users WHERE role = $1', ['admin']);

      // Prevent deleting the last admin
      if (adminCount.rows[0].count <= 1) {
        return res.status(400).json({ message: 'Cannot delete the last admin user' });
      }
    }

    // Delete the user
    await db.query('DELETE FROM users WHERE id = $1', [id]);

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Server error while deleting user' });
  }
};

// Get a single user by ID
const getUserById = async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ message: 'User ID is required' });
  }

  try {
    const result = await db.query(
      'SELECT id, name, email, role, created_at FROM users WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ message: 'Server error while fetching user' });
  }
};

// Get user profile for authenticated user
const getUserProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await db.query(
      'SELECT id, name, email, contact_number, address, province, role, created_at FROM users WHERE id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: 'Server error while fetching profile' });
  }
};

// Update user profile for authenticated user
const updateUserProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, email, contact_number, address, province } = req.body;

    // Check if user exists
    const existingUser = await db.query('SELECT * FROM users WHERE id = $1', [userId]);

    if (existingUser.rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Build the update query dynamically based on provided fields
    let updateQuery = 'UPDATE users SET ';
    const updateValues = [];
    const updateFields = [];
    let paramIndex = 1;

    if (name !== undefined) {
      updateFields.push(`name = $${paramIndex}`);
      updateValues.push(name);
      paramIndex++;
    }

    if (email !== undefined) {
      // Check if email is already taken by another user
      if (email !== existingUser.rows[0].email) {
        const emailCheck = await db.query('SELECT * FROM users WHERE email = $1 AND id != $2', [email, userId]);
        if (emailCheck.rows.length > 0) {
          return res.status(400).json({ message: 'Email is already taken by another user' });
        }
      }

      updateFields.push(`email = $${paramIndex}`);
      updateValues.push(email);
      paramIndex++;
    }

    if (contact_number !== undefined) {
      updateFields.push(`contact_number = $${paramIndex}`);
      updateValues.push(contact_number);
      paramIndex++;
    }

    if (address !== undefined) {
      updateFields.push(`address = $${paramIndex}`);
      updateValues.push(address);
      paramIndex++;
    }

    if (province !== undefined) {
      updateFields.push(`province = $${paramIndex}`);
      updateValues.push(province);
      paramIndex++;
    }

    // If no fields to update
    if (updateFields.length === 0) {
      return res.status(400).json({ message: 'No fields to update' });
    }

    // Complete the query
    updateQuery += updateFields.join(', ');
    updateQuery += ` WHERE id = $${paramIndex} RETURNING id, name, email, contact_number, address, province, role, created_at`;
    updateValues.push(userId);

    // Execute the update
    const result = await db.query(updateQuery, updateValues);

    res.json({
      message: 'Profile updated successfully',
      user: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({ message: 'Server error while updating profile' });
  }
};

module.exports = {
  getAllUsers,
  getCustomersFromOrders,
  getAllUsersAndCustomers,
  createUser,
  updateUser,
  deleteUser,
  getUserById,
  getUserProfile,
  updateUserProfile
};