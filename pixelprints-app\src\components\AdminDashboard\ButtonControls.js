import React, { useState, useEffect } from 'react';
import './ButtonControls.css';
import { getApiUrl } from '../../utils/api';

const ButtonControls = ({ settings, updateButtonControlSetting }) => {
  const [tshirtEnabled, setTshirtEnabled] = useState(true);
  const [mugEnabled, setMugEnabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    // Initialize state from settings
    if (settings.tshirt_button_enabled !== undefined) {
      setTshirtEnabled(settings.tshirt_button_enabled === 'true');
    }
    if (settings.mug_button_enabled !== undefined) {
      setMugEnabled(settings.mug_button_enabled === 'true');
    }
  }, [settings]);

  const handleTshirtToggle = async () => {
    setLoading(true);
    try {
      const newValue = !tshirtEnabled;
      await updateButtonControlSetting('tshirt_button_enabled', newValue.toString(), 'Whether the Design T-Shirt button is visible on the home page');
      setTshirtEnabled(newValue);
      setMessage(`T-Shirt button ${newValue ? 'enabled' : 'disabled'} successfully`);
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('Error updating T-Shirt button setting:', error);
      setMessage('Error updating T-Shirt button setting');
      setTimeout(() => setMessage(''), 3000);
    } finally {
      setLoading(false);
    }
  };

  const handleMugToggle = async () => {
    setLoading(true);
    try {
      const newValue = !mugEnabled;
      await updateButtonControlSetting('mug_button_enabled', newValue.toString(), 'Whether the Design Mug button is visible on the home page');
      setMugEnabled(newValue);
      setMessage(`Mug button ${newValue ? 'enabled' : 'disabled'} successfully`);
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('Error updating Mug button setting:', error);
      setMessage('Error updating Mug button setting');
      setTimeout(() => setMessage(''), 3000);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="button-controls">
      <h2>Button Controls</h2>
      <p>Control the visibility of design buttons on the home page</p>

      {message && (
        <div className={`message ${message.includes('Error') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      <div className="control-section">
        <div className="control-item">
          <div className="control-info">
            <h3>Design T-Shirt Button</h3>
            <p>Controls whether the "Design a T-Shirt" button is visible on the home page</p>
          </div>
          <div className="control-toggle">
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={tshirtEnabled}
                onChange={handleTshirtToggle}
                disabled={loading}
              />
              <span className="slider"></span>
            </label>
            <span className={`status ${tshirtEnabled ? 'enabled' : 'disabled'}`}>
              {tshirtEnabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>

        <div className="control-item">
          <div className="control-info">
            <h3>Design Mug Button</h3>
            <p>Controls whether the "Design a Mug" button is visible on the home page</p>
          </div>
          <div className="control-toggle">
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={mugEnabled}
                onChange={handleMugToggle}
                disabled={loading}
              />
              <span className="slider"></span>
            </label>
            <span className={`status ${mugEnabled ? 'enabled' : 'disabled'}`}>
              {mugEnabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>
      </div>

      <div className="control-info-box">
        <h4>How it works:</h4>
        <ul>
          <li>When enabled, the button will be visible to all users on the home page</li>
          <li>When disabled, the button will be hidden from the home page</li>
          <li>Changes take effect immediately for all users</li>
          <li>Users who are already on the T-Shirt designer page can continue using it even if the button is disabled</li>
        </ul>
      </div>
    </div>
  );
};

export default ButtonControls;
