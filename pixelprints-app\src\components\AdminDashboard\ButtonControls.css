.button-controls {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.button-controls h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.button-controls > p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 14px;
}

.message {
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-weight: 500;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.control-section {
  margin-bottom: 30px;
}

.control-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 16px;
  background: #fafafa;
}

.control-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.control-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
  max-width: 400px;
}

.control-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

input:disabled + .slider {
  opacity: 0.6;
  cursor: not-allowed;
}

.status {
  font-weight: 600;
  font-size: 14px;
  min-width: 70px;
}

.status.enabled {
  color: #28a745;
}

.status.disabled {
  color: #dc3545;
}

.control-info-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.control-info-box h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.control-info-box ul {
  margin: 0;
  padding-left: 20px;
}

.control-info-box li {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.control-info-box li:last-child {
  margin-bottom: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .control-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .control-toggle {
    align-self: flex-end;
  }

  .control-info p {
    max-width: none;
  }
}
