const db = require('../db');
require('dotenv').config();

async function updateUsersTableProfile() {
  try {
    // Add profile fields to users table
    await db.query(`
      ALTER TABLE users
      ADD COLUMN IF NOT EXISTS contact_number VARCHAR(20),
      ADD COLUMN IF NOT EXISTS address TEXT,
      ADD COLUMN IF NOT EXISTS province VARCHAR(100);
    `);

    console.log('Users table updated successfully with profile fields');
    process.exit(0);
  } catch (error) {
    console.error('Error updating users table with profile fields:', error);
    process.exit(1);
  }
}

updateUsersTableProfile();
